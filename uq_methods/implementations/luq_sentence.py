import numpy as np
import logging
from typing import List, Dict, Any, <PERSON><PERSON>, Optional
import random

from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator
from core.embedding_cache import get_embedding_encoder

log = logging.getLogger(__name__)


def sentence_matching(
    text_a: List[str], 
    text_b: List[str], 
    pct_k: float, 
    mode: str,
    embedding_model: str = "intfloat/multilingual-e5-large-instruct"
) -> Tuple[List[str], List[str]]:
    """
    Match sentences between two lists based on embedding similarity.
    
    Args:
        text_a: First list of sentences
        text_b: Second list of sentences  
        pct_k: Percentage of minimum list length to return (0.0 to 1.0)
        mode: Selection mode - "bottom" (most similar), "top" (least similar), or "random"
        embedding_model: Model name for computing embeddings
        
    Returns:
        Tuple of (matched_sentences_from_a, matched_sentences_from_b) with same length
    """
    if not text_a or not text_b:
        return [], []
    
    if not 0.0 <= pct_k <= 1.0:
        raise ValueError("pct_k must be between 0.0 and 1.0")
    
    if mode not in ["bottom", "top", "random"]:
        raise ValueError("mode must be 'bottom', 'top', or 'random'")
    
    # Determine number of matches to return
    min_length = min(len(text_a), len(text_b))
    num_matches = max(1, int(pct_k * min_length))
    
    # Get embedding encoder
    encoder = get_embedding_encoder(embedding_model)
    
    # Compute embeddings for both lists
    embeddings_a = np.array([encoder.encode_one(sent, normalize=True) for sent in text_a])
    embeddings_b = np.array([encoder.encode_one(sent, normalize=True) for sent in text_b])
    
    # Compute distance matrix (1 - cosine similarity)
    similarity_matrix = embeddings_a @ embeddings_b.T
    distance_matrix = 1.0 - similarity_matrix
    
    # Find best matches for the shorter list
    if len(text_a) <= len(text_b):
        shorter_list, longer_list = text_a, text_b
        distance_for_matching = distance_matrix  # rows=shorter, cols=longer
        reverse_order = False
    else:
        shorter_list, longer_list = text_b, text_a
        distance_for_matching = distance_matrix.T  # rows=shorter, cols=longer
        reverse_order = True
    
    # For each sentence in shorter list, find best match in longer list
    matches = []
    used_longer_indices = set()
    
    for i in range(len(shorter_list)):
        # Find available matches (not already used)
        available_distances = []
        for j in range(len(longer_list)):
            if j not in used_longer_indices:
                available_distances.append((distance_for_matching[i, j], i, j))
        
        if available_distances:
            # Sort by distance (ascending = most similar first)
            available_distances.sort(key=lambda x: x[0])
            best_distance, shorter_idx, longer_idx = available_distances[0]
            matches.append((best_distance, shorter_idx, longer_idx))
            used_longer_indices.add(longer_idx)
    
    # Sort matches by distance for selection
    matches.sort(key=lambda x: x[0])
    
    # Select matches based on mode
    if mode == "bottom":  # Most similar (smallest distances)
        selected_matches = matches[:num_matches]
    elif mode == "top":   # Least similar (largest distances)
        selected_matches = matches[-num_matches:]
    else:  # random
        selected_matches = random.sample(matches, min(num_matches, len(matches)))
    
    # Extract matched sentences
    matched_shorter = [shorter_list[match[1]] for match in selected_matches]
    matched_longer = [longer_list[match[2]] for match in selected_matches]
    
    # Return in correct order based on original input
    if reverse_order:
        return matched_longer, matched_shorter
    else:
        return matched_shorter, matched_longer


class LUQSENTENCEUQ(BaseUQMethod):
    """
    LUQSENTENCE: Sentence-Level LUQ Uncertainty Quantification
    
    A new UQ method that performs sentence-level matching and NLI computation:
    1. Splits responses into sentences
    2. Uses embedding-based matching to pair similar sentences across responses
    3. Performs NLI on matched sentence pairs instead of sentence-to-full-response
    4. Computes uncertainty based on consistency of matched pairs
    """

    def __init__(
        self,
        nli_model_name: str = "microsoft/deberta-large-mnli",
        embedding_model: str = "intfloat/multilingual-e5-large-instruct",
        pct_k: float = 0.8,
        matching_mode: str = "bottom",
        verbose: bool = False
    ):
        """
        Initialize LUQSENTENCE uncertainty quantification method.

        Args:
            nli_model_name: NLI model name for consistency checking
            embedding_model: Embedding model for sentence matching
            pct_k: Percentage of minimum sentence count to match (0.0 to 1.0)
            matching_mode: "bottom" (most similar), "top" (least similar), or "random"
            verbose: Whether to print debug information
        """
        self.nli_model_name = nli_model_name
        self.embedding_model = embedding_model
        self.pct_k = pct_k
        self.matching_mode = matching_mode
        self.verbose = verbose

        # Initialize NLI calculator for consistency checking
        self.nli_calc = get_nli_calculator(nli_model_name)

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences for LUQSENTENCE analysis.

        Args:
            text: Input text to split

        Returns:
            List of sentences suitable for NLI analysis
        """
        # Clean and preprocess the text first
        text = self._preprocess_text(text)
        if not text:
            return []

        # Use sentence-splitter for sentence segmentation
        sentences = self._nlp_sentence_split(text)

        if sentences and self.verbose:
            log.info(f"Split text ({len(text)} chars) into {len(sentences)} sentences")

        return sentences

    def _preprocess_text(self, text: str) -> str:
        """
        Clean text by removing markdown formatting and noise.

        Args:
            text: Raw input text

        Returns:
            Cleaned text ready for sentence segmentation
        """
        import re

        text = text.strip()
        if not text:
            return ""

        # Remove markdown formatting
        text = re.sub(r'^(#{1,6})\s+(.+)$', r'\2.', text, flags=re.MULTILINE)  # Headers
        text = re.sub(r'^[-=*_]{3,}$', '', text, flags=re.MULTILINE)  # Separators
        text = re.sub(r'\*\*\*(.*?)\*\*\*', r'\1', text)  # Bold italic
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)      # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)          # Italic
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # Links

        # Remove list markers
        text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

        # Normalize whitespace
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = ' '.join(text.split())

        return text

    def _nlp_sentence_split(self, text: str) -> List[str]:
        """
        Split text into sentences using sentence-splitter library.
        """
        from sentence_splitter import SentenceSplitter
        splitter = SentenceSplitter(language='en')
        sentences = splitter.split(text)
        if self.verbose:
            log.info(f"Used sentence-splitter, found {len(sentences)} sentences")
        return sentences

    def _compute_nli_score(self, premise: str, hypothesis: str) -> float:
        """
        Compute NLI-based entailment probability following LUQ paper approach.

        Args:
            premise: First sentence
            hypothesis: Second sentence

        Returns:
            Entailment probability score
        """
        nli_result = self.nli_calc.compute_nli_scores_cached(premise, hypothesis)

        # LUQ paper: normalize between entailment and contradiction only
        entail_prob = nli_result.entailment
        contradict_prob = nli_result.contradiction

        total_prob = entail_prob + contradict_prob
        if total_prob > 0:
            entail_score = entail_prob / total_prob
        else:
            # If both are 0, default to neutral (0.5)
            entail_score = 0.5

        return entail_score

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute LUQSENTENCE uncertainty score for the given responses.

        Args:
            responses: List of response texts to evaluate

        Returns:
            Dictionary containing uncertainty scores and detailed metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 1.0,
                "error": "Need at least 2 responses for LUQSENTENCE computation",
                "method": "LUQSENTENCE"
            }

        try:
            if self.verbose:
                log.info(f"Starting LUQSENTENCE computation for {len(responses)} responses")

            # Split responses into sentences
            sentences_list = []
            for i, response in enumerate(responses):
                sentences = self._split_into_sentences(response)
                sentences_list.append(sentences)
                if self.verbose:
                    log.info(f"Response {i+1} split into {len(sentences)} sentences")

            # Compute detailed LUQSENTENCE scores
            detailed_results = self._compute_luqsentence_detailed(sentences_list)

            return {
                "uncertainty_score": detailed_results["overall_uncertainty"],
                "method": "LUQSENTENCE",
                "num_responses": len(responses),
                "luqsentence_scores_per_sample": detailed_results["luqsentence_scores_per_sample"],
                "consistency_scores_per_sample": detailed_results["consistency_scores_per_sample"],
                "overall_consistency": detailed_results["overall_consistency"],
                "num_sentences_per_response": detailed_results["num_sentences_per_response"],
                "matching_details": detailed_results["matching_details"],
                "nli_details": detailed_results["nli_details"],
                "metadata": {
                    "nli_model": self.nli_model_name,
                    "embedding_model": self.embedding_model,
                    "pct_k": self.pct_k,
                    "matching_mode": self.matching_mode,
                    "total_sentence_pairs": sum(len(detail["nli_computations"]) for detail in detailed_results["nli_details"])
                }
            }

        except Exception as e:
            log.error(f"Error computing LUQSENTENCE uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "LUQSENTENCE"
            }

    def _compute_luqsentence_detailed(self, sentences_list: List[List[str]]) -> Dict[str, Any]:
        """
        Core LUQSENTENCE algorithm implementation.

        For each pair of response samples:
        1. Use sentence matching to find corresponding sentences
        2. Check consistency of matched sentence pairs using NLI
        3. Compute average consistency scores
        4. Convert to uncertainty scores

        Args:
            sentences_list: List of sentence lists for each response

        Returns:
            LUQSENTENCE results with matching and NLI details
        """
        num_samples = len(sentences_list)

        # Initialize result containers
        luqsentence_scores_per_sample = np.zeros(num_samples)
        consistency_scores_per_sample = np.zeros(num_samples)
        num_sentences_per_response = [len(sample) for sample in sentences_list]

        # Detailed tracking for analysis
        nli_details = []
        matching_details = []

        for index, sentences in enumerate(sentences_list):
            if self.verbose:
                log.info(f"Processing sample {index+1}/{num_samples} with sentence matching")

            # Get other samples for comparison
            other_samples = [(i, sample) for i, sample in enumerate(sentences_list) if i != index]

            if not other_samples or not sentences:
                # Default values for edge cases
                luqsentence_scores_per_sample[index] = 0.5
                consistency_scores_per_sample[index] = 0.5
                continue

            # Compute consistency scores using sentence matching
            all_scores = []
            sample_nli_computations = []
            sample_matching_info = []

            for other_index, other_sentences in other_samples:
                # Perform sentence matching
                matched_current, matched_other = sentence_matching(
                    sentences, other_sentences, self.pct_k, self.matching_mode, self.embedding_model
                )

                if self.verbose:
                    log.info(f"Matched {len(matched_current)} sentence pairs between samples {index+1} and {other_index+1}")

                # Store matching information
                sample_matching_info.append({
                    "other_sample_index": other_index,
                    "num_matched_pairs": len(matched_current),
                    "original_lengths": [len(sentences), len(other_sentences)],
                    "pct_k": self.pct_k,
                    "mode": self.matching_mode,
                    "matched_sentences": [
                        {"current": curr[:50] + "..." if len(curr) > 50 else curr,
                         "other": other[:50] + "..." if len(other) > 50 else other}
                        for curr, other in zip(matched_current, matched_other)
                    ]
                })

                # Compute NLI scores for matched pairs
                pair_scores = []
                for sent_current, sent_other in zip(matched_current, matched_other):
                    # Compute bidirectional NLI scores
                    nli_forward = self.nli_calc.compute_nli_scores_cached(sent_current, sent_other)
                    nli_backward = self.nli_calc.compute_nli_scores_cached(sent_other, sent_current)

                    # Use LUQSENTENCE scoring method (entailment vs contradiction)
                    score_forward = self._compute_nli_score(sent_current, sent_other)
                    score_backward = self._compute_nli_score(sent_other, sent_current)

                    # Average bidirectional scores
                    avg_score = (score_forward + score_backward) / 2.0
                    pair_scores.append(avg_score)

                    # Store detailed NLI info
                    sample_nli_computations.append({
                        "current_sent": sent_current[:50] + "..." if len(sent_current) > 50 else sent_current,
                        "other_sent": sent_other[:50] + "..." if len(sent_other) > 50 else sent_other,
                        "other_sample_idx": other_index,
                        "nli_forward": {
                            "e": round(float(nli_forward.entailment), 3),
                            "n": round(float(nli_forward.neutral), 3),
                            "d": round(float(nli_forward.contradiction), 3)
                        },
                        "nli_backward": {
                            "e": round(float(nli_backward.entailment), 3),
                            "n": round(float(nli_backward.neutral), 3),
                            "d": round(float(nli_backward.contradiction), 3)
                        },
                        "luqsentence_score_forward": round(float(score_forward), 3),
                        "luqsentence_score_backward": round(float(score_backward), 3),
                        "avg_luqsentence_score": round(float(avg_score), 3)
                    })

                if pair_scores:
                    all_scores.extend(pair_scores)

            # Calculate sample-level metrics
            if all_scores:
                sample_consistency = np.mean(all_scores)
                sample_uncertainty = 1.0 - sample_consistency
            else:
                sample_consistency = 0.5
                sample_uncertainty = 0.5

            consistency_scores_per_sample[index] = sample_consistency
            luqsentence_scores_per_sample[index] = sample_uncertainty

            # Store sample details
            nli_details.append({
                "sample_index": index,
                "num_sentences": len(sentences),
                "num_other_samples": len(other_samples),
                "total_matched_pairs": len(all_scores),
                "sample_consistency": round(float(sample_consistency), 4),
                "sample_uncertainty": round(float(sample_uncertainty), 4),
                "nli_computations": sample_nli_computations
            })

            matching_details.append({
                "sample_index": index,
                "matching_info": sample_matching_info
            })

        # Calculate overall metrics
        overall_uncertainty = luqsentence_scores_per_sample.mean()
        overall_consistency = consistency_scores_per_sample.mean()

        return {
            "overall_uncertainty": float(overall_uncertainty),
            "luqsentence_scores_per_sample": luqsentence_scores_per_sample.tolist(),
            "consistency_scores_per_sample": consistency_scores_per_sample.tolist(),
            "overall_consistency": float(overall_consistency),
            "num_sentences_per_response": num_sentences_per_response,
            "nli_details": nli_details,
            "matching_details": matching_details
        }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 2

    def get_method_name(self) -> str:
        """Get the method name."""
        return "LUQSENTENCE"

    def __str__(self):
        return f"LUQSENTENCE(pct_k={self.pct_k}, mode={self.matching_mode})"
