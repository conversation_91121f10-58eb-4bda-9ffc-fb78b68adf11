#!/usr/bin/env python3
"""
Quick collection checker
"""

from pymongo import MongoClient
import json

def main():
    try:
        client = MongoClient('localhost', 27017, serverSelectionTimeoutMS=5000)
        db = client['LLM-UQ']
        
        print('📊 Checking MongoDB collections...')
        
        # List all collections
        collections = db.list_collection_names()
        relevant_cols = [col for col in collections if 'counterfactual' in col.lower() or 'UQ_result' in col or 'conterfatual' in col]
        
        print(f'\n🔍 Found {len(relevant_cols)} relevant collections:')
        for col in sorted(relevant_cols):
            count = db[col].count_documents({})
            print(f'  {col}: {count} documents')
        
        # Check LUQSENTENCE test results
        test_col = 'UQ_result_LUQSENTENCE_counterfactual_test'
        if test_col in collections:
            count = db[test_col].count_documents({})
            if count > 0:
                sample = db[test_col].find_one({})
                print(f'\n🧠 LUQSENTENCE test results sample:')
                print(f'  Count: {count}')
                print(f'  Structure: {list(sample.keys())}')
                print(f'  Group key example: {sample["group_key"]}')
                print(f'  UQ methods: {list(sample["uq_results"].keys())}')
        
        # Check target collection
        target_col = 'UQ_results_conterfatual_qa'
        if target_col in collections:
            count = db[target_col].count_documents({})
            print(f'\n🎯 Target collection ({target_col}):')
            print(f'  Count: {count}')
            if count > 0:
                sample = db[target_col].find_one({})
                print(f'  Structure: {list(sample.keys())}')
                print(f'  UQ methods: {list(sample["uq_results"].keys())}')
                
                # Check if LUQSENTENCE already exists
                luq_count = db[target_col].count_documents({"uq_results.LUQSENTENCEUQ": {"$exists": True}})
                print(f'  Documents with LUQSENTENCE: {luq_count}')
        
        client.close()
        print('\n✅ Collection check completed')
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    main()
