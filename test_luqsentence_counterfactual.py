#!/usr/bin/env python3
"""
Test LUQSENTENCE method using real counterfactual responses from MongoDB.
This script retrieves counterfactual responses and compares LUQSENTENCE with original LUQ.
"""

import sys
import os
import logging
import numpy as np
from typing import List, Dict, Any
from pymongo import MongoClient

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.luq_sentence import LUQSENTENCEUQ, sentence_matching
from uq_methods.implementations.luq import LUQUQ
from core.embedding_cache import get_embedding_encoder

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def complete_sentence_matching(
    text_a: List[str],
    text_b: List[str],
    mode: str = "bottom"
) -> tuple[List[str], List[str]]:
    """
    完整句子匹配：匹配较短文本中的所有句子

    Args:
        text_a: 第一个句子列表
        text_b: 第二个句子列表
        mode: 匹配模式 ("bottom" 最相似优先)

    Returns:
        匹配的句子对，长度为min(len(text_a), len(text_b))
        较短文本中的每个句子都会被匹配
    """
    if not text_a or not text_b:
        return [], []

    # 获取embedding编码器
    encoder = get_embedding_encoder("intfloat/multilingual-e5-large-instruct")

    # 计算embeddings
    embeddings_a = np.array([encoder.encode_one(sent, normalize=True) for sent in text_a])
    embeddings_b = np.array([encoder.encode_one(sent, normalize=True) for sent in text_b])

    # 计算相似度矩阵
    similarity_matrix = embeddings_a @ embeddings_b.T
    distance_matrix = 1.0 - similarity_matrix

    # 确定较短和较长的列表
    if len(text_a) <= len(text_b):
        shorter_list, longer_list = text_a, text_b
        distance_for_matching = distance_matrix  # rows=shorter, cols=longer
        reverse_order = False
    else:
        shorter_list, longer_list = text_b, text_a
        distance_for_matching = distance_matrix.T  # rows=shorter, cols=longer
        reverse_order = True

    matched_shorter = []
    matched_longer = []
    used_longer_indices = set()

    # 为较短列表中的每个句子找到最佳匹配
    for i in range(len(shorter_list)):
        # 找到可用的匹配（未被使用的较长列表中的句子）
        available_matches = []
        for j in range(len(longer_list)):
            if j not in used_longer_indices:
                available_matches.append((distance_for_matching[i, j], j))

        if available_matches:
            # 按距离排序（最相似的在前）
            available_matches.sort(key=lambda x: x[0])
            _, best_longer_idx = available_matches[0]

            matched_shorter.append(shorter_list[i])
            matched_longer.append(longer_list[best_longer_idx])
            used_longer_indices.add(best_longer_idx)
        else:
            # 如果没有可用匹配，找到最相似的（即使已被使用）
            distances = [distance_for_matching[i, j] for j in range(len(longer_list))]
            best_longer_idx = np.argmin(distances)

            matched_shorter.append(shorter_list[i])
            matched_longer.append(longer_list[best_longer_idx])

    # 根据原始顺序返回结果
    if reverse_order:
        return matched_longer, matched_shorter
    else:
        return matched_shorter, matched_longer


def get_counterfactual_samples(limit: int = 5) -> List[Dict[str, Any]]:
    """
    获取多个counterfactual样本用于测试
    
    Args:
        limit: 获取的样本数量
        
    Returns:
        List of sample dictionaries with responses and metadata
    """
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        collection = db["response_collections"]
        
        pipeline = [
            {"$match": {"task_name": "counterfactual_qa", "dataset_source": "counterfactual_data"}},
            {"$group": {
                "_id": {"category": "$category", "row_index": "$row_index", "prompt_seed": "$prompt_seed"},
                "responses": {"$push": "$parsed_answer"},
                "count": {"$sum": 1},
                "sample_doc": {"$first": "$$ROOT"}  # 保留一个文档用于获取元数据
            }},
            {"$match": {"count": {"$gte": 3}}},  # 至少3个响应
            {"$limit": limit}
        ]
        
        result = list(collection.aggregate(pipeline))
        client.close()
        
        samples = []
        for group in result:
            sample = {
                "category": group["_id"]["category"],
                "row_index": group["_id"]["row_index"],
                "prompt_seed": group["_id"]["prompt_seed"],
                "responses": group["responses"][:5],  # 最多取5个响应
                "count": len(group["responses"][:5]),
                "prompt_text": group["sample_doc"].get("prompt_text", ""),
                "task_id": group["sample_doc"].get("task_id", "")
            }
            samples.append(sample)
            
        log.info(f"获取到 {len(samples)} 个counterfactual样本")
        return samples
        
    except Exception as e:
        log.error(f"获取样本失败: {e}")
        return []


def demonstrate_sentence_matching(sample: Dict[str, Any]):
    """
    演示句子匹配过程，显示详细的匹配结果
    """
    responses = sample["responses"][:2]  # 只取前两个响应进行演示
    category = sample["category"]
    row_index = sample["row_index"]

    log.info(f"\n{'='*80}")
    log.info(f"句子匹配演示: {category} - 问题 {row_index}")
    log.info(f"{'='*80}")

    # 分割句子
    from uq_methods.implementations.luq_sentence import LUQSENTENCEUQ
    luq_sentence = LUQSENTENCEUQ()

    sentences_a = luq_sentence._split_into_sentences(responses[0])
    sentences_b = luq_sentence._split_into_sentences(responses[1])

    log.info(f"\n响应A分割为 {len(sentences_a)} 个句子:")
    for i, sent in enumerate(sentences_a[:5]):  # 只显示前5个句子
        log.info(f"  A{i+1}: {sent}")
    if len(sentences_a) > 5:
        log.info(f"  ... 还有 {len(sentences_a) - 5} 个句子")

    log.info(f"\n响应B分割为 {len(sentences_b)} 个句子:")
    for i, sent in enumerate(sentences_b[:5]):  # 只显示前5个句子
        log.info(f"  B{i+1}: {sent}")
    if len(sentences_b) > 5:
        log.info(f"  ... 还有 {len(sentences_b) - 5} 个句子")

    # 匹配较短文本中的所有句子
    matching_configs = [
        {"pct_k": 1.0, "mode": "bottom", "name": "完整匹配（较短文本的所有句子）"},
    ]

    for config in matching_configs:
        log.info(f"\n{'-'*60}")
        log.info(f"匹配配置: {config['name']} (pct_k={config['pct_k']}, mode={config['mode']})")
        log.info(f"{'-'*60}")

        try:
            # 对于100%匹配，我们需要特殊处理
            if config["pct_k"] == 1.0:
                matched_a, matched_b = complete_sentence_matching(
                    sentences_a, sentences_b, config["mode"]
                )
            else:
                matched_a, matched_b = sentence_matching(
                    sentences_a, sentences_b,
                    config["pct_k"], config["mode"]
                )

            log.info(f"匹配到 {len(matched_a)} 对句子:")

            # 初始化NLI计算器用于演示
            from core.nli_shared import get_nli_calculator
            nli_calc = get_nli_calculator("microsoft/deberta-large-mnli")

            for i, (sent_a, sent_b) in enumerate(zip(matched_a, matched_b)):
                log.info(f"\n{'='*80}")
                log.info(f"匹配对 {i+1}/{len(matched_a)}:")
                log.info(f"{'='*80}")

                # 显示完整句子内容
                log.info(f"句子A: {sent_a}")
                log.info(f"句子B: {sent_b}")

                # 计算NLI分数
                try:
                    # 双向NLI计算
                    nli_a_to_b = nli_calc.compute_nli_scores_cached(sent_a, sent_b)
                    nli_b_to_a = nli_calc.compute_nli_scores_cached(sent_b, sent_a)

                    # 计算LUQSENTENCE分数（entailment vs contradiction）
                    def compute_luq_score(nli_result):
                        entail_prob = nli_result.entailment
                        contradict_prob = nli_result.contradiction
                        total_prob = entail_prob + contradict_prob
                        if total_prob > 0:
                            return entail_prob / total_prob
                        else:
                            return 0.5

                    luq_score_a_to_b = compute_luq_score(nli_a_to_b)
                    luq_score_b_to_a = compute_luq_score(nli_b_to_a)
                    avg_luq_score = (luq_score_a_to_b + luq_score_b_to_a) / 2.0

                    log.info(f"\nNLI结果:")
                    log.info(f"  A→B: Entailment={nli_a_to_b.entailment:.4f}, Neutral={nli_a_to_b.neutral:.4f}, Contradiction={nli_a_to_b.contradiction:.4f}")
                    log.info(f"  B→A: Entailment={nli_b_to_a.entailment:.4f}, Neutral={nli_b_to_a.neutral:.4f}, Contradiction={nli_b_to_a.contradiction:.4f}")
                    log.info(f"\nLUQ分数计算:")
                    log.info(f"  A→B LUQ分数: {luq_score_a_to_b:.4f}")
                    log.info(f"  B→A LUQ分数: {luq_score_b_to_a:.4f}")
                    log.info(f"  平均LUQ分数: {avg_luq_score:.4f}")
                    log.info(f"  一致性评估: {'高' if avg_luq_score > 0.7 else '中' if avg_luq_score > 0.4 else '低'}")

                except Exception as e:
                    log.error(f"NLI计算失败: {e}")

            # 显示未匹配的句子
            log.info(f"\n{'='*80}")
            log.info(f"未匹配的句子分析")
            log.info(f"{'='*80}")

            # 找出哪些句子没有被匹配
            matched_indices_a = set()
            matched_indices_b = set()

            # 重新计算匹配来获取索引信息
            encoder = get_embedding_encoder("intfloat/multilingual-e5-large-instruct")
            embeddings_a = np.array([encoder.encode_one(sent, normalize=True) for sent in sentences_a])
            embeddings_b = np.array([encoder.encode_one(sent, normalize=True) for sent in sentences_b])
            similarity_matrix = embeddings_a @ embeddings_b.T
            distance_matrix = 1.0 - similarity_matrix

            # 找到匹配的索引
            if len(sentences_a) <= len(sentences_b):
                shorter_list, longer_list = sentences_a, sentences_b
                distance_for_matching = distance_matrix
                reverse_order = False
            else:
                shorter_list, longer_list = sentences_b, sentences_a
                distance_for_matching = distance_matrix.T
                reverse_order = True

            matches = []
            used_longer_indices = set()

            for i in range(len(shorter_list)):
                available_distances = []
                for j in range(len(longer_list)):
                    if j not in used_longer_indices:
                        available_distances.append((distance_for_matching[i, j], i, j))

                if available_distances:
                    available_distances.sort(key=lambda x: x[0])
                    best_distance, shorter_idx, longer_idx = available_distances[0]
                    matches.append((best_distance, shorter_idx, longer_idx))
                    used_longer_indices.add(longer_idx)

            # 选择最相似的匹配
            matches.sort(key=lambda x: x[0])
            num_matches = max(1, int(config["pct_k"] * min(len(sentences_a), len(sentences_b))))
            selected_matches = matches[:num_matches]

            # 记录匹配的索引
            for _, shorter_idx, longer_idx in selected_matches:
                if reverse_order:
                    matched_indices_b.add(shorter_idx)
                    matched_indices_a.add(longer_idx)
                else:
                    matched_indices_a.add(shorter_idx)
                    matched_indices_b.add(longer_idx)

            # 显示未匹配的句子A
            unmatched_a = [i for i in range(len(sentences_a)) if i not in matched_indices_a]
            log.info(f"\n响应A中未匹配的句子 ({len(unmatched_a)}/{len(sentences_a)}):")
            for i, idx in enumerate(unmatched_a[:10]):  # 只显示前10个
                log.info(f"  A{idx+1}: {sentences_a[idx]}")
            if len(unmatched_a) > 10:
                log.info(f"  ... 还有 {len(unmatched_a) - 10} 个未匹配的句子")

            # 显示未匹配的句子B
            unmatched_b = [i for i in range(len(sentences_b)) if i not in matched_indices_b]
            log.info(f"\n响应B中未匹配的句子 ({len(unmatched_b)}/{len(sentences_b)}):")
            for i, idx in enumerate(unmatched_b[:10]):  # 只显示前10个
                log.info(f"  B{idx+1}: {sentences_b[idx]}")
            if len(unmatched_b) > 10:
                log.info(f"  ... 还有 {len(unmatched_b) - 10} 个未匹配的句子")

            # 统计信息
            total_sentences = len(sentences_a) + len(sentences_b)
            matched_sentences = len(matched_a) * 2  # 每个匹配对包含2个句子
            unmatched_sentences = total_sentences - matched_sentences

            log.info(f"\n匹配统计:")
            log.info(f"  总句子数: {total_sentences}")
            log.info(f"  已匹配句子数: {matched_sentences} ({matched_sentences/total_sentences*100:.1f}%)")
            log.info(f"  未匹配句子数: {unmatched_sentences} ({unmatched_sentences/total_sentences*100:.1f}%)")

        except Exception as e:
            log.error(f"匹配失败: {e}")


def test_luqsentence_vs_luq(sample: Dict[str, Any]) -> Dict[str, Any]:
    """
    比较LUQSENTENCE和原始LUQ方法
    
    Args:
        sample: 包含responses和元数据的样本
        
    Returns:
        比较结果字典
    """
    responses = sample["responses"]
    category = sample["category"]
    row_index = sample["row_index"]
    
    log.info(f"\n{'='*60}")
    log.info(f"测试样本: {category} - 问题 {row_index}")
    log.info(f"响应数量: {len(responses)}")
    log.info(f"{'='*60}")
    
    # 显示响应内容概览
    for i, response in enumerate(responses):
        log.info(f"响应 {i+1}: {len(response)} 字符 - {response[:100]}...")
    
    results = {
        "sample_info": {
            "category": category,
            "row_index": row_index,
            "num_responses": len(responses),
            "avg_response_length": sum(len(r) for r in responses) / len(responses)
        }
    }
    
    # 测试原始LUQ
    log.info(f"\n--- 测试原始LUQ ---")
    try:
        luq = LUQUQ(verbose=False)
        luq_result = luq.compute_uncertainty(responses)
        results["LUQ"] = {
            "uncertainty_score": luq_result.get("uncertainty_score"),
            "overall_consistency": luq_result.get("overall_consistency"),
            "method": luq_result.get("method"),
            "error": luq_result.get("error")
        }
        log.info(f"LUQ 不确定性分数: {luq_result.get('uncertainty_score', 'N/A'):.4f}")
        log.info(f"LUQ 整体一致性: {luq_result.get('overall_consistency', 'N/A'):.4f}")
    except Exception as e:
        log.error(f"LUQ 测试失败: {e}")
        results["LUQ"] = {"error": str(e)}
    
    # 测试LUQSENTENCE - 不同配置
    luqsentence_configs = [
        {"pct_k": 0.5, "matching_mode": "bottom", "name": "LUQSENTENCE_0.5_bottom"},
        {"pct_k": 0.8, "matching_mode": "bottom", "name": "LUQSENTENCE_0.8_bottom"},
        {"pct_k": 1.0, "matching_mode": "bottom", "name": "LUQSENTENCE_1.0_bottom"},
        {"pct_k": 0.8, "matching_mode": "top", "name": "LUQSENTENCE_0.8_top"},
    ]
    
    for config in luqsentence_configs:
        log.info(f"\n--- 测试 {config['name']} ---")
        try:
            luqsentence = LUQSENTENCEUQ(
                pct_k=config["pct_k"],
                matching_mode=config["matching_mode"],
                verbose=False
            )
            luqsentence_result = luqsentence.compute_uncertainty(responses)
            
            results[config["name"]] = {
                "uncertainty_score": luqsentence_result.get("uncertainty_score"),
                "overall_consistency": luqsentence_result.get("overall_consistency"),
                "method": luqsentence_result.get("method"),
                "config": {
                    "pct_k": config["pct_k"],
                    "matching_mode": config["matching_mode"]
                },
                "total_sentence_pairs": luqsentence_result.get("metadata", {}).get("total_sentence_pairs", 0),
                "error": luqsentence_result.get("error")
            }
            
            log.info(f"{config['name']} 不确定性分数: {luqsentence_result.get('uncertainty_score', 'N/A'):.4f}")
            log.info(f"{config['name']} 整体一致性: {luqsentence_result.get('overall_consistency', 'N/A'):.4f}")
            log.info(f"{config['name']} 句子对数量: {luqsentence_result.get('metadata', {}).get('total_sentence_pairs', 0)}")
            
        except Exception as e:
            log.error(f"{config['name']} 测试失败: {e}")
            results[config["name"]] = {"error": str(e)}
    
    return results


def analyze_results(all_results: List[Dict[str, Any]]):
    """分析所有测试结果"""
    log.info(f"\n{'='*80}")
    log.info("结果分析汇总")
    log.info(f"{'='*80}")
    
    # 统计成功的测试
    successful_tests = [r for r in all_results if "LUQ" in r and "error" not in r["LUQ"]]
    log.info(f"成功测试的样本数: {len(successful_tests)}/{len(all_results)}")
    
    if not successful_tests:
        log.error("没有成功的测试结果可供分析")
        return
    
    # 计算平均分数
    methods = ["LUQ", "LUQSENTENCE_0.5_bottom", "LUQSENTENCE_0.8_bottom", 
               "LUQSENTENCE_1.0_bottom", "LUQSENTENCE_0.8_top"]
    
    log.info(f"\n平均不确定性分数:")
    for method in methods:
        scores = []
        for result in successful_tests:
            if method in result and "uncertainty_score" in result[method] and result[method]["uncertainty_score"] is not None:
                scores.append(result[method]["uncertainty_score"])
        
        if scores:
            avg_score = sum(scores) / len(scores)
            log.info(f"  {method}: {avg_score:.4f} (基于 {len(scores)} 个样本)")
        else:
            log.info(f"  {method}: 无有效数据")
    
    # 比较LUQSENTENCE与LUQ的差异
    log.info(f"\nLUQSENTENCE vs LUQ 差异分析:")
    for method in methods[1:]:  # 跳过LUQ本身
        differences = []
        for result in successful_tests:
            if ("LUQ" in result and method in result and 
                "uncertainty_score" in result["LUQ"] and "uncertainty_score" in result[method] and
                result["LUQ"]["uncertainty_score"] is not None and result[method]["uncertainty_score"] is not None):
                diff = result[method]["uncertainty_score"] - result["LUQ"]["uncertainty_score"]
                differences.append(diff)
        
        if differences:
            avg_diff = sum(differences) / len(differences)
            log.info(f"  {method} - LUQ: {avg_diff:+.4f} 平均差异 (基于 {len(differences)} 个样本)")


def main():
    """主函数"""
    log.info("🧪 LUQSENTENCE Counterfactual 测试")
    log.info("使用MongoDB中的真实counterfactual响应数据")
    log.info("=" * 80)
    
    # 获取测试数据
    samples = get_counterfactual_samples(limit=3)  # 先测试3个样本
    if not samples:
        log.error("❌ 无法获取测试样本")
        return 1
    
    log.info(f"📊 将测试 {len(samples)} 个counterfactual样本")

    # 只演示句子匹配过程，显示完整的NLI计算
    if samples:
        log.info(f"\n🎯 演示句子匹配和NLI计算过程")
        demonstrate_sentence_matching(samples[0])
    else:
        log.error("没有可用的样本进行演示")
    
    log.info(f"\n{'='*80}")
    log.info("测试完成!")
    log.info(f"{'='*80}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
