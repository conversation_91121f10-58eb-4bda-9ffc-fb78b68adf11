#!/usr/bin/env python3
"""
Verify LUQSENTENCE merge results
"""

from pymongo import MongoClient

def main():
    try:
        client = MongoClient('localhost', 27017, serverSelectionTimeoutMS=5000)
        db = client['LLM-UQ']
        collection = db['UQ_result_counterfactual_qa']
        
        print('🎉 LUQSENTENCE合并结果验证')
        print('=' * 50)
        
        total = collection.count_documents({})
        print(f'总文档数: {total}')
        
        # 检查LUQSENTENCE结果
        luq_count = collection.count_documents({'uq_results.LUQSENTENCEUQ': {'$exists': True}})
        print(f'包含LUQSENTENCE的文档: {luq_count}')
        
        # 获取所有UQ方法
        all_methods = set()
        for doc in collection.find({}, {'uq_results': 1}):
            methods = list(doc.get('uq_results', {}).keys())
            all_methods.update(methods)
        
        print(f'\n📊 所有UQ方法: {sorted(all_methods)}')
        
        # 统计每种方法
        print('\n📈 方法分布:')
        for method in sorted(all_methods):
            count = collection.count_documents({f'uq_results.{method}': {'$exists': True}})
            print(f'  {method}: {count} documents')
        
        # 显示LUQSENTENCE示例
        if luq_count > 0:
            sample = collection.find_one({'uq_results.LUQSENTENCEUQ': {'$exists': True}})
            luq_result = sample['uq_results']['LUQSENTENCEUQ']
            
            print(f'\n🧠 LUQSENTENCE示例结果:')
            print(f'  UQ值: {luq_result.get("uq_value", "N/A"):.4f}')
            print(f'  状态: {luq_result.get("status", "N/A")}')
            
            if 'full_result' in luq_result:
                full = luq_result['full_result']
                print(f'  不确定性分数: {full.get("uncertainty_score", "N/A"):.4f}')
                print(f'  整体一致性: {full.get("overall_consistency", "N/A"):.4f}')
                print(f'  响应数量: {full.get("num_responses", "N/A")}')
                
                if 'metadata' in full:
                    meta = full['metadata']
                    print(f'  总句子对数: {meta.get("total_sentence_pairs", "N/A")}')
                    print(f'  总NLI计算数: {meta.get("total_nli_computations", "N/A")}')
        
        print(f'\n✅ 合并验证完成!')
        
        client.close()
        
    except Exception as e:
        print(f'❌ 错误: {e}')

if __name__ == "__main__":
    main()
