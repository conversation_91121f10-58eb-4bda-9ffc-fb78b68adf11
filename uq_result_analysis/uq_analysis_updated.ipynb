# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def setup_paper_style():
    """设置论文发表风格 - 大字体版本"""
    plt.rcParams.update({
        'font.size': 20,           # 增大基础字体
        'axes.titlesize': 20,      # 增大标题字体
        'axes.labelsize': 18,      # 增大坐标轴标签字体
        'xtick.labelsize': 18,     # 增大x轴刻度字体
        'ytick.labelsize': 18,     # 增大y轴刻度字体
        'legend.fontsize': 18,     # 增大图例字体
        'figure.titlesize': 22,    # 增大图形标题字体
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

# 应用大字体设置
setup_paper_style()

# 创建输出目录
figures_dir = Path('figures')
figures_dir.mkdir(parents=True, exist_ok=True)

print("环境设置完成 - 大字体论文风格")
print(f"图表将保存到: {figures_dir}")
print("字体设置:")
print("  - 基础字体: 16pt")
print("  - 标题字体: 20pt")
print("  - 坐标轴标签: 18pt")
print("  - 刻度标签: 16pt")
print("  - 图例字体: 14pt")

# 加载数据
data_dir = Path('uq_result_analysis/data')
df = pd.read_csv(data_dir / 'combined_uq_results.csv')

# 加载数据摘要
with open(data_dir / 'data_summary.json', 'r', encoding='utf-8') as f:
    summary = json.load(f)

print(f"数据加载完成")
print(f"总记录数: {len(df)}")
print(f"数据形状: {df.shape}")
print(f"\n列名: {list(df.columns)}")

# 数据预处理和清洗
print("=== 数据概览 ===")
print(df.info())
print("\n=== 缺失值统计 ===")
print(df.isnull().sum())

# 清理数据
# 移除uq_value为空的记录
df_clean = df.dropna(subset=['uq_value']).copy()
# 过滤掉unknown模型
df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()

print(f"\n清理后数据量: {len(df_analysis)}")

# 基本统计信息
print("\n=== 基本统计 ===")
print(f"模型数量: {df_analysis['llm_model'].nunique()}")
print(f"任务数量: {df_analysis['task_name'].nunique()}")
print(f"UQ方法数量: {df_analysis['uq_method'].nunique()}")

print("\n模型分布:")
print(df_analysis['llm_model'].value_counts())

print("\n任务分布:")
print(df_analysis['task_name'].value_counts())

print("\nUQ方法分布:")
print(df_analysis['uq_method'].value_counts())

def normalize_uq_values(df_analysis):
    """对每个UQ方法的值进行归一化处理"""
    print("对UQ值进行归一化处理...")
    
    df_normalized = df_analysis.copy()
    normalization_stats = {}
    
    # 为每个UQ方法单独进行归一化
    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']
        
        # 计算归一化参数
        min_val = method_values.min()
        max_val = method_values.max()
        mean_val = method_values.mean()
        std_val = method_values.std()
        
        # 使用Min-Max归一化到[0,1]
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0  # 如果所有值相同，归一化为0
        
        df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values
        
        # 同时计算Z-score归一化
        if std_val != 0:
            z_score_values = (method_values - mean_val) / std_val
        else:
            z_score_values = method_values * 0
        
        df_normalized.loc[method_mask, 'uq_value_zscore'] = z_score_values
        
        # 保存归一化统计信息
        normalization_stats[method] = {
            'original_min': float(min_val),
            'original_max': float(max_val),
            'original_mean': float(mean_val),
            'original_std': float(std_val),
            'range': float(max_val - min_val),
            'cv': float(std_val/mean_val) if mean_val != 0 else 0
        }
    
    print("归一化完成")
    print("归一化方法:")
    print("  - uq_value_normalized: Min-Max归一化到[0,1]")
    print("  - uq_value_zscore: Z-score标准化")
    
    return df_normalized, normalization_stats

# 执行归一化
df_analysis, normalization_stats = normalize_uq_values(df_analysis)

# 显示归一化统计
print("\n=== 归一化统计摘要 ===")
for method, stats in normalization_stats.items():
    print(f"{method}:")
    print(f"  原始范围: [{stats['original_min']:.4f}, {stats['original_max']:.4f}]")
    print(f"  原始均值: {stats['original_mean']:.4f}")
    print(f"  变异系数: {stats['cv']:.4f}")

def create_individual_analysis(model, task, method, subset, save_plots=True):
    """为单个model-task-method组合创建分析图表"""
    
    if len(subset) < 5:
        print(f"跳过 {model}-{task}-{method}: 样本数不足 ({len(subset)})")
        return
    
    print(f"分析: {model} - {task} - {method} (样本数: {len(subset)})")
    
    # 1. 分布直方图
    fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
    ax1.hist(subset['uq_value'], bins=min(20, len(subset)//2), alpha=0.7, 
            color='skyblue', edgecolor='black')
    ax1.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {subset["uq_value"].mean():.3f}')
    ax1.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
               label=f'Median: {subset["uq_value"].median():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title(f'{model} - {task} - {method}\nDistribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename1 = f"{model}_{task}_{method}_distribution.pdf"
        plt.savefig(figures_dir / filename1, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 2. Violin图
    fig2, ax2 = plt.subplots(1, 1, figsize=(8, 10))
    parts = ax2.violinplot([subset['uq_value']], positions=[1], showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_facecolor('lightgreen')
        pc.set_alpha(0.7)
    ax2.set_ylabel('UQ Value')
    ax2.set_title(f'{model} - {task} - {method}\nViolin Plot')
    ax2.set_xticks([1])
    ax2.set_xticklabels([method], rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename2 = f"{model}_{task}_{method}_violin.pdf"
        plt.savefig(figures_dir / filename2, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 3. 统计信息表格
    fig3, ax3 = plt.subplots(1, 1, figsize=(10, 8))
    
    stats_data = [
        ['Count', f"{len(subset)}"],
        ['Mean', f"{subset['uq_value'].mean():.4f}"],
        ['Std', f"{subset['uq_value'].std():.4f}"],
        ['Variance', f"{subset['uq_value'].var():.4f}"],
        ['Median', f"{subset['uq_value'].median():.4f}"],
        ['Min', f"{subset['uq_value'].min():.4f}"],
        ['Max', f"{subset['uq_value'].max():.4f}"],
        ['Q25', f"{subset['uq_value'].quantile(0.25):.4f}"],
        ['Q75', f"{subset['uq_value'].quantile(0.75):.4f}"]
    ]
    
    table = ax3.table(cellText=stats_data,
                     colLabels=['Statistic', 'Value'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(16)
    table.scale(1.5, 2.0)
    ax3.axis('off')
    ax3.set_title(f'{model} - {task} - {method}\nStatistics Summary')
    
    plt.tight_layout()
    if save_plots:
        filename3 = f"{model}_{task}_{method}_statistics.pdf"
        plt.savefig(figures_dir / filename3, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

# 获取所有有效的组合
combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
valid_combinations = combinations[combinations['count'] >= 5]  # 只分析样本数>=5的组合

print(f"发现 {len(valid_combinations)} 个有效的model-task-uq method组合")
valid_combinations

# 示例：分析前3个组合
print("=== 示例：分析前3个组合 ===")

for idx, row in valid_combinations.head(3).iterrows():
    model = row['llm_model']
    task = row['task_name']
    method = row['uq_method']
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task) & 
        (df_analysis['uq_method'] == method)
    ]
    
    create_individual_analysis(model, task, method, subset, save_plots=True)
    print("-" * 50)

def create_model_task_comparison(model, task, subset, save_plots=True):
    """为model-task组合创建UQ方法比较图表"""
    
    if len(subset) < 50:
        print(f"跳过 {model}-{task}: 样本数不足 ({len(subset)})")
        return
    
    methods = subset['uq_method'].unique()
    colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
    
    print(f"比较分析: {model} - {task} (样本数: {len(subset)}, 方法数: {len(methods)})")
    
    # 1. 原始值分布对比（密度曲线）
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value']
        # 绘制密度曲线
        method_data.plot.density(ax=ax1, alpha=0.7, label=method, color=colors[i], linewidth=3)
    
    ax1.set_xlabel('Original UQ Value')
    ax1.set_ylabel('Density')
    ax1.set_title(f'{model} - {task}\nOriginal Values Density Distribution')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename1 = f"{model}_{task}_original_distribution.pdf"
        plt.savefig(figures_dir / filename1, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 2. 原始值violin图
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    method_data_orig = [subset[subset['uq_method'] == method]['uq_value'].values for method in methods]
    parts = ax2.violinplot(method_data_orig, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax2.set_title(f'{model} - {task}\nOriginal Values Violin Plot')
    ax2.set_xlabel('UQ Method')
    ax2.set_ylabel('Original UQ Value')
    ax2.set_xticks(range(len(methods)))
    ax2.set_xticklabels(methods, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename2 = f"{model}_{task}_original_violin.pdf"
        plt.savefig(figures_dir / filename2, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 3. 归一化值分布对比（密度曲线）
    fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value_normalized']
        # 绘制密度曲线
        method_data.plot.density(ax=ax3, alpha=0.7, label=method, color=colors[i], linewidth=3)
    
    ax3.set_xlabel('Normalized UQ Value [0,1]')
    ax3.set_ylabel('Density')
    ax3.set_title(f'{model} - {task}\nNormalized Values Density Distribution (Min-Max)')
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename3 = f"{model}_{task}_normalized_distribution.pdf"
        plt.savefig(figures_dir / filename3, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 4. 归一化值violin图
    fig4, ax4 = plt.subplots(1, 1, figsize=(12, 8))
    method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]
    parts = ax4.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax4.set_title(f'{model} - {task}\nNormalized Values Violin Plot')
    ax4.set_xlabel('UQ Method')
    ax4.set_ylabel('Normalized UQ Value [0,1]')
    ax4.set_xticks(range(len(methods)))
    ax4.set_xticklabels(methods, rotation=45)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename4 = f"{model}_{task}_normalized_violin.pdf"
        plt.savefig(figures_dir / filename4, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 5. 统计比较表格
    fig5, ax5 = plt.subplots(1, 1, figsize=(14, 8))
    stats_comparison = []
    for method in methods:
        method_subset = subset[subset['uq_method'] == method]
        stats_comparison.append([
            method,
            f"{len(method_subset)}",
            f"{method_subset['uq_value'].mean():.3f}",
            f"{method_subset['uq_value_normalized'].mean():.3f}",
            f"{method_subset['uq_value_zscore'].mean():.3f}",
            f"{method_subset['uq_value_normalized'].std():.3f}"
        ])
    
    table = ax5.table(cellText=stats_comparison,
                     colLabels=['Method', 'Count', 'Orig Mean', 'Norm Mean', 'Z-score Mean', 'Norm Std'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(14)
    table.scale(1.5, 2.0)
    ax5.axis('off')
    ax5.set_title(f'{model} - {task}\nComparison Statistics')
    
    plt.tight_layout()
    if save_plots:
        filename5 = f"{model}_{task}_comparison_statistics.pdf"
        plt.savefig(figures_dir / filename5, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

# 获取model-task组合
model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index(name='count')
valid_mt_combinations = model_task_combinations[model_task_combinations['count'] >= 50]

print(f"发现 {len(valid_mt_combinations)} 个有效的model-task组合")
print(valid_mt_combinations)

# 示例：分析第一个model-task组合
if len(valid_mt_combinations) > 0:
    print("=== 示例：分析第一个model-task组合 ===")
    
    row = valid_mt_combinations.iloc[0]
    model = row['llm_model']
    task = row['task_name']
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    create_model_task_comparison(model, task, subset, save_plots=True)
else:
    print("没有找到有效的model-task组合")

# 1. 整体分布（密度曲线）
fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))

# 绘制密度曲线
df_analysis['uq_value'].plot.density(ax=ax1, color='skyblue', linewidth=4, alpha=0.8)

# 添加均值和中位数线
ax1.axvline(df_analysis['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
           label=f'Mean: {df_analysis["uq_value"].mean():.3f}')
ax1.axvline(df_analysis['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
           label=f'Median: {df_analysis["uq_value"].median():.3f}')

ax1.set_xlabel('UQ Value')
ax1.set_ylabel('Density')
ax1.set_title('Overall UQ Value Density Distribution')
ax1.legend()
ax1.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'overall_distribution.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 打印整体统计
print("=== 整体UQ值统计 ===")
print(f"均值: {df_analysis['uq_value'].mean():.4f}")
print(f"标准差: {df_analysis['uq_value'].std():.4f}")
print(f"方差: {df_analysis['uq_value'].var():.4f}")
print(f"中位数: {df_analysis['uq_value'].median():.4f}")
print(f"最小值: {df_analysis['uq_value'].min():.4f}")
print(f"最大值: {df_analysis['uq_value'].max():.4f}")

# 2. 按模型分布（密度曲线）
fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
for model in df_analysis['llm_model'].unique():
    model_data = df_analysis[df_analysis['llm_model'] == model]['uq_value']
    # 绘制密度曲线
    model_data.plot.density(ax=ax2, alpha=0.7, label=model, linewidth=3)
ax2.set_xlabel('UQ Value')
ax2.set_ylabel('Density')
ax2.set_title('UQ Value Density Distribution by Model')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'distribution_by_model.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 3. 按任务分布（密度曲线）
fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
for task in df_analysis['task_name'].unique():
    task_data = df_analysis[df_analysis['task_name'] == task]['uq_value']
    # 绘制密度曲线
    task_data.plot.density(ax=ax3, alpha=0.7, label=task, linewidth=3)
ax3.set_xlabel('UQ Value')
ax3.set_ylabel('Density')
ax3.set_title('UQ Value Density Distribution by Task')
ax3.legend()
ax3.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'distribution_by_task.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 4. 按UQ方法分布（选择主要方法，密度曲线）
fig4, ax4 = plt.subplots(1, 1, figsize=(14, 8))
top_methods = df_analysis['uq_method'].value_counts().head(6).index
for method in top_methods:
    method_data = df_analysis[df_analysis['uq_method'] == method]['uq_value']
    # 绘制密度曲线
    method_data.plot.density(ax=ax4, alpha=0.7, label=method, linewidth=3)
ax4.set_xlabel('UQ Value')
ax4.set_ylabel('Density')
ax4.set_title('UQ Value Density Distribution by Top UQ Methods')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'distribution_by_method.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 1. Model-Task平均UQ值热力图
fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
pivot_mean = df_analysis.pivot_table(values='uq_value', index='llm_model', 
                                     columns='task_name', aggfunc='mean')
sns.heatmap(pivot_mean, annot=True, fmt='.3f', cmap='RdYlBu_r', 
            ax=ax1, cbar_kws={'label': 'Mean UQ Value'})
ax1.set_title('Mean UQ Value by Model and Task')
ax1.set_xlabel('Task')
ax1.set_ylabel('Model')

plt.tight_layout()
plt.savefig(figures_dir / 'heatmap_mean_uq_value.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 2. Model-Task标准差热力图
fig2, ax2 = plt.subplots(1, 1, figsize=(10, 8))
pivot_std = df_analysis.pivot_table(values='uq_value', index='llm_model', 
                                   columns='task_name', aggfunc='std')
sns.heatmap(pivot_std, annot=True, fmt='.3f', cmap='Reds', 
            ax=ax2, cbar_kws={'label': 'Std UQ Value'})
ax2.set_title('UQ Value Standard Deviation by Model and Task')
ax2.set_xlabel('Task')
ax2.set_ylabel('Model')

plt.tight_layout()
plt.savefig(figures_dir / 'heatmap_std_uq_value.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 3. Model-Method热力图（使用归一化值）
fig3, ax3 = plt.subplots(1, 1, figsize=(14, 8))
pivot_model_method = df_analysis.pivot_table(values='uq_value_normalized', index='llm_model', 
                                             columns='uq_method', aggfunc='mean')
sns.heatmap(pivot_model_method, annot=True, fmt='.3f', cmap='viridis', 
            ax=ax3, cbar_kws={'label': 'Mean Normalized UQ Value'})
ax3.set_title('Mean Normalized UQ Value by Model and Method')
ax3.set_xlabel('UQ Method')
ax3.set_ylabel('Model')

plt.tight_layout()
plt.savefig(figures_dir / 'heatmap_model_method_normalized.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 4. Task-Method热力图（使用归一化值）
fig4, ax4 = plt.subplots(1, 1, figsize=(14, 8))
pivot_task_method = df_analysis.pivot_table(values='uq_value_normalized', index='task_name', 
                                           columns='uq_method', aggfunc='mean')
sns.heatmap(pivot_task_method, annot=True, fmt='.3f', cmap='plasma', 
            ax=ax4, cbar_kws={'label': 'Mean Normalized UQ Value'})
ax4.set_title('Mean Normalized UQ Value by Task and Method')
ax4.set_xlabel('UQ Method')
ax4.set_ylabel('Task')

plt.tight_layout()
plt.savefig(figures_dir / 'heatmap_task_method_normalized.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 计算统计信息
method_stats_orig = df_analysis.groupby('uq_method')['uq_value'].agg([
    'count', 'mean', 'std', 'median', 'min', 'max'
]).round(4)

method_stats_norm = df_analysis.groupby('uq_method')['uq_value_normalized'].agg([
    'count', 'mean', 'std', 'median', 'min', 'max'
]).round(4)

print("=== UQ方法原始值统计 ===")
print(method_stats_orig.sort_values('mean', ascending=False))

print("\n=== UQ方法归一化值统计 ===")
print(method_stats_norm.sort_values('mean', ascending=False))

# 1. 原始值violin图
methods = df_analysis['uq_method'].unique()

fig1, ax1 = plt.subplots(1, 1, figsize=(14, 8))
method_data_orig = [df_analysis[df_analysis['uq_method'] == method]['uq_value'].values for method in methods]
parts = ax1.violinplot(method_data_orig, positions=range(len(methods)), showmeans=True, showmedians=True)
for pc in parts['bodies']:
    pc.set_alpha(0.7)
ax1.set_title('Overall Original Values Distribution by Method')
ax1.set_xlabel('UQ Method')
ax1.set_ylabel('Original UQ Value')
ax1.set_xticks(range(len(methods)))
ax1.set_xticklabels(methods, rotation=45)
ax1.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'overall_original_violin.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 2. 归一化值violin图
fig2, ax2 = plt.subplots(1, 1, figsize=(14, 8))
method_data_norm = [df_analysis[df_analysis['uq_method'] == method]['uq_value_normalized'].values for method in methods]
parts = ax2.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
for pc in parts['bodies']:
    pc.set_alpha(0.7)
ax2.set_title('Overall Normalized Values Distribution by Method [0,1]')
ax2.set_xlabel('UQ Method')
ax2.set_ylabel('Normalized UQ Value')
ax2.set_xticks(range(len(methods)))
ax2.set_xticklabels(methods, rotation=45)
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'overall_normalized_violin.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 3. 归一化效果比较表格
fig3, ax3 = plt.subplots(1, 1, figsize=(16, 10))
comparison_data = []
for method in methods:
    orig_stats = method_stats_orig.loc[method]
    norm_stats = method_stats_norm.loc[method]
    comparison_data.append([
        method,
        f"{orig_stats['count']}",
        f"{orig_stats['mean']:.3f}",
        f"{norm_stats['mean']:.3f}",
        f"{orig_stats['std']:.3f}",
        f"{norm_stats['std']:.3f}",
        f"{orig_stats['std']/orig_stats['mean']:.3f}" if orig_stats['mean'] != 0 else "N/A"
    ])

table = ax3.table(cellText=comparison_data,
                 colLabels=['Method', 'Count', 'Orig Mean', 'Norm Mean', 'Orig Std', 'Norm Std', 'Orig CV'],
                 cellLoc='center',
                 loc='center')
table.auto_set_font_size(False)
table.set_fontsize(14)
table.scale(1.5, 2.0)
ax3.axis('off')
ax3.set_title('Overall Normalization Comparison')

plt.tight_layout()
plt.savefig(figures_dir / 'overall_normalization_comparison.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()

# 生成总结报告
print("=== UQ分析总结报告 ===")
print(f"\n1. 数据概况:")
print(f"   - 总样本数: {len(df_analysis)}")
print(f"   - 模型数量: {df_analysis['llm_model'].nunique()}")
print(f"   - 任务数量: {df_analysis['task_name'].nunique()}")
print(f"   - UQ方法数量: {df_analysis['uq_method'].nunique()}")

print(f"\n2. UQ值整体统计:")
print(f"   - 均值: {df_analysis['uq_value'].mean():.4f}")
print(f"   - 标准差: {df_analysis['uq_value'].std():.4f}")
print(f"   - 中位数: {df_analysis['uq_value'].median():.4f}")
print(f"   - 范围: [{df_analysis['uq_value'].min():.4f}, {df_analysis['uq_value'].max():.4f}]")

print(f"\n3. 模型表现 (按平均UQ值排序):")
model_performance = df_analysis.groupby('llm_model')['uq_value'].agg(['mean', 'std', 'count']).round(4)
model_performance = model_performance.sort_values('mean')
for model, stats in model_performance.iterrows():
    print(f"   - {model}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 样本数={stats['count']}")

print(f"\n4. 任务难度 (按平均UQ值排序):")
task_difficulty = df_analysis.groupby('task_name')['uq_value'].agg(['mean', 'std', 'count']).round(4)
task_difficulty = task_difficulty.sort_values('mean', ascending=False)
for task, stats in task_difficulty.iterrows():
    print(f"   - {task}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 样本数={stats['count']}")

print(f"\n5. UQ方法效果 (按归一化平均值排序):")
method_effectiveness = df_analysis.groupby('uq_method')['uq_value_normalized'].agg(['mean', 'std', 'count']).round(4)
method_effectiveness = method_effectiveness.sort_values('mean', ascending=False)
for method, stats in method_effectiveness.iterrows():
    print(f"   - {method}: 归一化均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 样本数={stats['count']}")

print(f"\n6. 归一化处理效果:")
print(f"   - 已对 {len(normalization_stats)} 个UQ方法进行归一化")
print(f"   - 原始值范围差异巨大，归一化后可进行有效比较")
print(f"   - 使用Min-Max [0,1] 和 Z-score 两种归一化方法")

print(f"\n7. 图表输出:")
pdf_files = list(figures_dir.glob("*.pdf"))
print(f"   - 共生成 {len(pdf_files)} 个PDF图表")
print(f"   - 所有图表使用大字体，适合论文展示")
print(f"   - 每个图表单独保存，便于使用")

print(f"\n分析完成！所有图表已保存到: {figures_dir}")