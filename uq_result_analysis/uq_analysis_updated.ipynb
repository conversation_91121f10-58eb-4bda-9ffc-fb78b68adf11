# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def setup_paper_style():
    """设置论文发表风格 - 大字体版本"""
    plt.rcParams.update({
        'font.size': 20,           # 增大基础字体
        'axes.titlesize': 20,      # 增大标题字体
        'axes.labelsize': 18,      # 增大坐标轴标签字体
        'xtick.labelsize': 18,     # 增大x轴刻度字体
        'ytick.labelsize': 18,     # 增大y轴刻度字体
        'legend.fontsize': 18,     # 增大图例字体
        'figure.titlesize': 22,    # 增大图形标题字体
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

# 应用大字体设置
setup_paper_style()

# 创建输出目录
figures_dir = Path('figures')
figures_dir.mkdir(parents=True, exist_ok=True)

print("环境设置完成 - 大字体论文风格")
print(f"图表将保存到: {figures_dir}")
print("字体设置:")
print("  - 基础字体: 16pt")
print("  - 标题字体: 20pt")
print("  - 坐标轴标签: 18pt")
print("  - 刻度标签: 16pt")
print("  - 图例字体: 14pt")

# 读取/构建 df_analysis（确保后续统计代码可用）

def load_df_analysis():
    # 若已存在 df_analysis 且结构完整，直接返回
    if 'df_analysis' in globals():
        required_cols = {'llm_model','task_name','uq_method','uq_value'}
        if required_cols.issubset(set(df_analysis.columns)):
            print(f"已存在 df_analysis, 复用: {df_analysis.shape}")
            return df_analysis.copy()
    
    # 若已有清洗后的 df_clean, 直接复制作为分析数据
    if 'df_clean' in globals():
        print(f"使用现有 df_clean 构建 df_analysis: {df_clean.shape}")
        return df_clean.copy()
    
    data_dir = Path('uq_result_analysis/data')
    candidates = [
        data_dir / 'df_analysis.parquet',
        data_dir / 'df_analysis.csv',
        data_dir / 'uq_results.parquet',
        data_dir / 'uq_results.csv'
    ]
    
    for fp in candidates:
        if fp.exists():
            if fp.suffix == '.parquet':
                df = pd.read_parquet(fp)
            else:
                df = pd.read_csv(fp)
            print(f"已从文件加载: {fp} -> {df.shape}")
            return df
    
    # 尝试汇总 JSON / JSONL
    records = []
    if data_dir.exists():
        for fp in data_dir.glob('UQ_result_*.json*'):
            try:
                if fp.suffix == '.jsonl':
                    with fp.open('r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                records.append(json.loads(line))
                else:
                    with fp.open('r', encoding='utf-8') as f:
                        obj = json.load(f)
                        if isinstance(obj, list):
                            records.extend(obj)
                        else:
                            records.append(obj)
            except Exception as e:
                print(f"跳过 {fp}: {e}")
    if records:
        df = pd.DataFrame(records)
        print(f"已从 JSON 汇总构建: {df.shape}")
        return df
    
    raise FileNotFoundError("未找到可用的数据源，请确认数据文件已放置在 uq_result_analysis/data 目录。")

df_analysis = load_df_analysis()

# 基础清洗（去重、类型转换）
df_analysis = df_analysis.drop_duplicates(subset=['document_id','uq_method']).reset_index(drop=True)

# 确保关键列存在
for col in ['uncertainty_score']:
    if col not in df_analysis.columns and 'uq_value' in df_analysis.columns:
        df_analysis[col] = df_analysis['uq_value']

# 统计摘要
summary_basic = {
    'total_records': len(df_analysis),
    'unique_models': df_analysis['llm_model'].nunique() if 'llm_model' in df_analysis else None,
    'unique_tasks': df_analysis['task_name'].nunique() if 'task_name' in df_analysis else None,
    'unique_methods': df_analysis['uq_method'].nunique() if 'uq_method' in df_analysis else None
}
print("df_analysis 加载完成:")
print(summary_basic)
display(df_analysis.head(3))

# 基于 (llm_model, task_name) 分组的UQ统计
group_cols = ['llm_model', 'task_name','uq_method']

def compute_group_stats(df, value_col, suffix=""):
    stats = (
        df.groupby(group_cols)[value_col]
          .agg(count='count',
               mean='mean',
               median='median',
               std='std',
               Q25=lambda x: x.quantile(0.25),
               Q75=lambda x: x.quantile(0.75),
               min='min',
               max='max')
          .reset_index()
    )
    if suffix:
        rename_map = {c: f"{c}{suffix}" for c in stats.columns if c not in group_cols and c != 'count'}
        # count 不重复添加后缀（保持一致）
        stats = stats.rename(columns=rename_map)
    return stats

uq_stats = compute_group_stats(df_analysis, 'uq_value', suffix="")
if 'uq_value_normalized' in df_analysis.columns:
    uq_norm_stats = compute_group_stats(df_analysis, 'uq_value_normalized', suffix="_norm")
    # 合并（count 相同，仅保留一份）
    merged_stats = uq_stats.merge(uq_norm_stats, on=group_cols, how='left')
else:
    merged_stats = uq_stats

# 排序
merged_stats = merged_stats.sort_values(group_cols).reset_index(drop=True)

# 保存
out_csv = figures_dir / 'uq_group_stats.csv'
merged_stats.to_csv(out_csv, index=False)
print(f"已生成分组统计并保存到: {out_csv}")
display(merged_stats)

def normalize_uq_values(df_analysis):
    """对每个UQ方法的值进行归一化处理"""
    print("对UQ值进行归一化处理...")
    
    df_normalized = df_analysis.copy()
    normalization_stats = {}
    
    # 为每个UQ方法单独进行归一化
    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']
        
        # 计算归一化参数
        min_val = method_values.min()
        max_val = method_values.max()
        mean_val = method_values.mean()
        std_val = method_values.std()
        
        # 使用Min-Max归一化到[0,1]
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0  # 如果所有值相同，归一化为0
        
        df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values
        
        # 同时计算Z-score归一化
        if std_val != 0:
            z_score_values = (method_values - mean_val) / std_val
        else:
            z_score_values = method_values * 0
        
        df_normalized.loc[method_mask, 'uq_value_zscore'] = z_score_values
        
        # 保存归一化统计信息
        normalization_stats[method] = {
            'original_min': float(min_val),
            'original_max': float(max_val),
            'original_mean': float(mean_val),
            'original_std': float(std_val),
            'range': float(max_val - min_val),
            'cv': float(std_val/mean_val) if mean_val != 0 else 0
        }
    
    print("归一化完成")
    print("归一化方法:")
    print("  - uq_value_normalized: Min-Max归一化到[0,1]")
    print("  - uq_value_zscore: Z-score标准化")
    
    return df_normalized, normalization_stats

# 执行归一化
df_analysis, normalization_stats = normalize_uq_values(df_analysis)

# 显示归一化统计
print("\n=== 归一化统计摘要 ===")
for method, stats in normalization_stats.items():
    print(f"{method}:")
    print(f"  原始范围: [{stats['original_min']:.4f}, {stats['original_max']:.4f}]")
    print(f"  原始均值: {stats['original_mean']:.4f}")
    print(f"  变异系数: {stats['cv']:.4f}")

def create_individual_analysis(model, task, method, subset, save_plots=True):
    """为单个model-task-method组合创建分析图表"""
    
    if len(subset) < 5:
        print(f"跳过 {model}-{task}-{method}: 样本数不足 ({len(subset)})")
        return
    
    print(f"分析: {model} - {task} - {method} (样本数: {len(subset)})")
    
    # 1. 分布直方图
    fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
    ax1.hist(subset['uq_value'], bins=min(20, len(subset)//2), alpha=0.7, 
            color='skyblue', edgecolor='black')
    ax1.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {subset["uq_value"].mean():.3f}')
    ax1.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
               label=f'Median: {subset["uq_value"].median():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title(f'{model} - {task} - {method}\nDistribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename1 = f"{model}_{task}_{method}_distribution.pdf"
        plt.savefig(figures_dir / filename1, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    
    # 3. 统计信息表格
    fig3, ax3 = plt.subplots(1, 1, figsize=(10, 8))
    
    
    plt.tight_layout()
    if save_plots:
        filename3 = f"{model}_{task}_{method}_statistics.pdf"
        plt.savefig(figures_dir / filename3, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

    # 保存CSV行 (llm_model, task_name, uq_method, count, mean, std, median, min, max, Q25, Q75)
    stats_row = {
        'llm_model': model,
        'task_name': task,
        'uq_method': method,
        'count': len(subset),
        'mean': subset['uq_value'].mean(),
        'std': subset['uq_value'].std(),
        'median': subset['uq_value'].median(),
        'min': subset['uq_value'].min(),
        'max': subset['uq_value'].max(),
        'Q25': subset['uq_value'].quantile(0.25),
        'Q75': subset['uq_value'].quantile(0.75),
    }
    csv_path = figures_dir / 'uq_method_statistics.csv'
    pd.DataFrame([stats_row]).to_csv(csv_path, index=False,
                                     mode='a',
                                     header=not csv_path.exists())
    print(f"已写入统计到CSV: {csv_path}")


def create_model_task_comparison(model, task, subset, save_plots=True):
    """为model-task组合创建UQ方法比较图表"""
    
    if len(subset) < 50:
        print(f"跳过 {model}-{task}: 样本数不足 ({len(subset)})")
        return
    
    methods = subset['uq_method'].unique()
    colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
    
    print(f"比较分析: {model} - {task} (样本数: {len(subset)}, 方法数: {len(methods)})")
    
    # 1. 原始值分布对比（密度曲线）
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value']
        # 绘制密度曲线
        method_data.plot.density(ax=ax1, alpha=0.7, label=method, color=colors[i], linewidth=3)
    
    ax1.set_xlabel('Original UQ Value')
    ax1.set_ylabel('Density')
    ax1.set_title(f'{model} - {task}\nOriginal Values Density Distribution')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename1 = f"{model}_{task}_original_distribution.pdf"
        plt.savefig(figures_dir / filename1, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 2. 原始值violin图
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    method_data_orig = [subset[subset['uq_method'] == method]['uq_value'].values for method in methods]
    parts = ax2.violinplot(method_data_orig, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax2.set_title(f'{model} - {task}\nOriginal Values Violin Plot')
    ax2.set_xlabel('UQ Method')
    ax2.set_ylabel('Original UQ Value')
    ax2.set_xticks(range(len(methods)))
    ax2.set_xticklabels(methods, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename2 = f"{model}_{task}_original_violin.pdf"
        plt.savefig(figures_dir / filename2, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 3. 归一化值分布对比（密度曲线）
    fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value_normalized']
        # 绘制密度曲线
        method_data.plot.density(ax=ax3, alpha=0.7, label=method, color=colors[i], linewidth=3)
    
    ax3.set_xlabel('Normalized UQ Value [0,1]')
    ax3.set_ylabel('Density')
    ax3.set_title(f'{model} - {task}\nNormalized Values Density Distribution (Min-Max)')
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename3 = f"{model}_{task}_normalized_distribution.pdf"
        plt.savefig(figures_dir / filename3, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 4. 归一化值violin图
    fig4, ax4 = plt.subplots(1, 1, figsize=(12, 8))
    method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]
    parts = ax4.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax4.set_title(f'{model} - {task}\nNormalized Values Violin Plot')
    ax4.set_xlabel('UQ Method')
    ax4.set_ylabel('Normalized UQ Value [0,1]')
    ax4.set_xticks(range(len(methods)))
    ax4.set_xticklabels(methods, rotation=45)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    if save_plots:
        filename4 = f"{model}_{task}_normalized_violin.pdf"
        plt.savefig(figures_dir / filename4, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 5. 统计比较表格
    fig5, ax5 = plt.subplots(1, 1, figsize=(14, 8))
    stats_comparison = []
    for method in methods:
        method_subset = subset[subset['uq_method'] == method]
        stats_comparison.append([
            method,
            f"{len(method_subset)}",
            f"{method_subset['uq_value'].mean():.3f}",
            f"{method_subset['uq_value_normalized'].mean():.3f}",
            f"{method_subset['uq_value_zscore'].mean():.3f}",
            f"{method_subset['uq_value_normalized'].std():.3f}"
        ])
    
    table = ax5.table(cellText=stats_comparison,
                     colLabels=['Method', 'Count', 'Orig Mean', 'Norm Mean', 'Z-score Mean', 'Norm Std'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(14)
    table.scale(1.5, 2.0)
    ax5.axis('off')
    ax5.set_title(f'{model} - {task}\nComparison Statistics')
    
    plt.tight_layout()
    if save_plots:
        filename5 = f"{model}_{task}_comparison_statistics.pdf"
        plt.savefig(figures_dir / filename5, format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

# 获取model-task组合
model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index(name='count')
valid_mt_combinations = model_task_combinations[model_task_combinations['count'] >= 50]

print(f"发现 {len(valid_mt_combinations)} 个有效的model-task组合")
print(valid_mt_combinations)

# 示例：分析第一个model-task组合
if len(valid_mt_combinations) > 0:
    print("=== 示例：分析第一个model-task组合 ===")
    
    row = valid_mt_combinations.iloc[0]
    model = row['llm_model']
    task = row['task_name']
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    create_model_task_comparison(model, task, subset, save_plots=True)
else:
    print("没有找到有效的model-task组合")

# 3. 按任务分布（密度曲线）
fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
for task in df_analysis['task_name'].unique():
    task_data = df_analysis[df_analysis['task_name'] == task]['uq_value']
    # 绘制密度曲线
    task_data.plot.density(ax=ax3, alpha=0.7, label=task, linewidth=3)
ax3.set_xlabel('UQ Value')
ax3.set_ylabel('Density')
ax3.set_title('UQ Value Density Distribution by Task')
ax3.legend()
ax3.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'distribution_by_task.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()