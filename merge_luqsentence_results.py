#!/usr/bin/env python3
"""
Merge LUQSENTENCE Results Script
将LUQSENTENCE的结果合并到UQ_results_conterfatual_qa collection中
"""

import sys
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, BulkWriteError

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

class LUQSentenceMerger:
    """LUQSENTENCE结果合并器"""
    
    def __init__(self, mongo_host: str = "localhost", mongo_port: int = 27017, db_name: str = "LLM-UQ"):
        self.client = MongoClient(mongo_host, mongo_port, serverSelectionTimeoutMS=5000)
        self.db = self.client[db_name]

        # Source collections (LUQSENTENCE results)
        self.source_test_col = self.db["UQ_result_LUQSENTENCE_counterfactual_test"]
        self.source_full_col = self.db["UQ_result_LUQSENTENCE_counterfactual"]

        # Auto-detect target collection
        self.target_col = self._find_target_collection()

    def _find_target_collection(self):
        """自动查找目标collection"""
        possible_names = [
            "UQ_result_counterfactual_qa",  # 实际存在的名称
            "UQ_results_conterfatual_qa",  # 用户指定的名称
            "UQ_results_counterfactual_qa"  # 可能的变体
        ]

        collections = self.db.list_collection_names()

        for name in possible_names:
            if name in collections:
                count = self.db[name].count_documents({})
                log.info(f"🎯 Found target collection: {name} ({count} documents)")
                return self.db[name]

        # 如果都不存在，使用用户指定的名称（会创建新的）
        log.warning(f"⚠️  Target collection not found, will create: {possible_names[0]}")
        return self.db[possible_names[0]]

    def check_collections(self):
        """检查collections状态"""
        log.info("🔍 检查collections状态...")
        
        # Check source collections
        test_count = self.source_test_col.count_documents({})
        full_count = self.source_full_col.count_documents({})
        target_count = self.target_col.count_documents({})
        
        log.info(f"📊 Source collections:")
        log.info(f"  LUQSENTENCE test results: {test_count}")
        log.info(f"  LUQSENTENCE full results: {full_count}")
        log.info(f"  Target collection: {target_count}")
        
        if test_count == 0 and full_count == 0:
            log.error("❌ No LUQSENTENCE results found!")
            return False
            
        # Check structure compatibility
        if target_count > 0:
            target_sample = self.target_col.find_one({})
            log.info(f"🎯 Target collection structure: {list(target_sample.keys())}")
            
            if test_count > 0:
                source_sample = self.source_test_col.find_one({})
                log.info(f"🧠 Source collection structure: {list(source_sample.keys())}")
                
                # Check if structures are compatible
                if self._check_structure_compatibility(source_sample, target_sample):
                    log.info("✅ Structures are compatible")
                else:
                    log.warning("⚠️  Structure differences detected, will adapt during merge")
        
        return True
    
    def _check_structure_compatibility(self, source_doc: Dict, target_doc: Dict) -> bool:
        """检查文档结构兼容性"""
        required_fields = ["group_key", "uq_results", "metadata"]
        
        for field in required_fields:
            if field not in source_doc or field not in target_doc:
                return False
                
        return True
    
    def _build_group_key_query(self, group_key: Dict[str, Any]) -> Dict[str, Any]:
        """构建用于匹配的group_key查询"""
        # 使用关键字段进行匹配
        query = {}
        
        # 必须匹配的字段
        required_fields = ["task_name", "dataset_source", "prompt_seed"]
        for field in required_fields:
            if field in group_key:
                query[f"group_key.{field}"] = group_key[field]
        
        # 可选匹配字段
        optional_fields = ["category", "row_index", "input_text"]
        for field in optional_fields:
            if field in group_key:
                query[f"group_key.{field}"] = group_key[field]
                
        return query
    
    def merge_results(self, source_collection_name: str, dry_run: bool = False):
        """合并结果到目标collection"""
        source_col = self.db[source_collection_name]
        source_count = source_col.count_documents({})
        
        if source_count == 0:
            log.warning(f"⚠️  Source collection {source_collection_name} is empty")
            return
            
        log.info(f"🔄 Merging {source_count} results from {source_collection_name}")
        
        merged_count = 0
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        for doc in source_col.find({}):
            try:
                group_key = doc["group_key"]
                luqsentence_result = doc["uq_results"]["LUQSENTENCEUQ"]
                
                # 构建查询来找到匹配的目标文档
                query = self._build_group_key_query(group_key)
                
                # 查找目标文档
                target_doc = self.target_col.find_one(query)
                
                if target_doc:
                    # 更新现有文档
                    if "LUQSENTENCEUQ" in target_doc.get("uq_results", {}):
                        log.debug(f"⏭️  Skipping existing LUQSENTENCE result for {group_key}")
                        skipped_count += 1
                        continue
                    
                    # 添加LUQSENTENCE结果
                    update_doc = {
                        "$set": {
                            "uq_results.LUQSENTENCEUQ": luqsentence_result,
                            "timestamps.updated_at": datetime.now(timezone.utc)
                        },
                        "$inc": {
                            "metadata.method_count": 1,
                            "metadata.successful_methods": 1
                        }
                    }
                    
                    if not dry_run:
                        self.target_col.update_one({"_id": target_doc["_id"]}, update_doc)
                    
                    updated_count += 1
                    log.debug(f"✅ Updated document with LUQSENTENCE result")
                    
                else:
                    # 创建新文档
                    new_doc = {
                        "group_key": group_key,
                        "llm_model": doc.get("llm_model", "qwen3-32b"),
                        "uq_results": {
                            "LUQSENTENCEUQ": luqsentence_result
                        },
                        "metadata": {
                            "n_responses": doc.get("metadata", {}).get("n_responses", 0),
                            "method_count": 1,
                            "successful_methods": 1,
                            "failed_methods": 0
                        },
                        "timestamps": {
                            "created_at": datetime.now(timezone.utc),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                    
                    if not dry_run:
                        self.target_col.insert_one(new_doc)
                    
                    merged_count += 1
                    log.debug(f"✅ Created new document with LUQSENTENCE result")
                
            except Exception as e:
                log.error(f"❌ Error processing document: {str(e)}")
                error_count += 1
                continue
        
        # 报告结果
        log.info(f"📊 Merge results for {source_collection_name}:")
        log.info(f"  New documents created: {merged_count}")
        log.info(f"  Existing documents updated: {updated_count}")
        log.info(f"  Documents skipped: {skipped_count}")
        log.info(f"  Errors: {error_count}")
        
        if dry_run:
            log.info("🔍 DRY RUN - No actual changes made")
    
    def run_merge(self, include_test: bool = True, include_full: bool = True, dry_run: bool = False):
        """运行完整的合并过程"""
        log.info("🚀 Starting LUQSENTENCE results merge")
        
        if not self.check_collections():
            log.error("❌ Collection check failed")
            return False
        
        if dry_run:
            log.info("🔍 Running in DRY RUN mode - no changes will be made")
        
        try:
            # Merge test results
            if include_test:
                log.info("📝 Merging test results...")
                self.merge_results("UQ_result_LUQSENTENCE_counterfactual_test", dry_run)
            
            # Merge full results
            if include_full:
                log.info("📊 Merging full results...")
                self.merge_results("UQ_result_LUQSENTENCE_counterfactual", dry_run)
            
            # Final summary
            final_count = self.target_col.count_documents({})
            luqsentence_count = self.target_col.count_documents({"uq_results.LUQSENTENCEUQ": {"$exists": True}})
            
            log.info("🎉 Merge completed successfully!")
            log.info(f"📊 Final statistics:")
            log.info(f"  Total documents in target collection: {final_count}")
            log.info(f"  Documents with LUQSENTENCE results: {luqsentence_count}")
            
            return True
            
        except Exception as e:
            log.error(f"❌ Merge failed: {str(e)}")
            return False
        
        finally:
            self.client.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Merge LUQSENTENCE results into UQ_results_conterfatual_qa")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode (no actual changes)")
    parser.add_argument("--test-only", action="store_true", help="Only merge test results")
    parser.add_argument("--full-only", action="store_true", help="Only merge full results")
    parser.add_argument("--check-only", action="store_true", help="Only check collections, don't merge")
    
    args = parser.parse_args()
    
    merger = LUQSentenceMerger()
    
    if args.check_only:
        merger.check_collections()
        return
    
    include_test = not args.full_only
    include_full = not args.test_only
    
    success = merger.run_merge(
        include_test=include_test,
        include_full=include_full,
        dry_run=args.dry_run
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
