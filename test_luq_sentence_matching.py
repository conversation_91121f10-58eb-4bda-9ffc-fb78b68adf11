#!/usr/bin/env python3
"""
Test script for the new LUQ Sentence Matching implementation.
Tests both the sentence_matching function and the LUQSentenceMatchingUQ class.
"""

import sys
import os
import logging
from typing import List

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.luq import sentence_matching, LUQSentenceMatchingUQ, LUQUQ

# Set up logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)


def test_sentence_matching():
    """Test the sentence_matching function with sample data."""
    print("=" * 60)
    print("Testing sentence_matching function")
    print("=" * 60)
    
    # Sample sentences for testing
    text_a = [
        "The weather is sunny today.",
        "I like to eat pizza for dinner.",
        "Machine learning is fascinating.",
        "The cat is sleeping on the couch."
    ]
    
    text_b = [
        "It's a beautiful sunny day outside.",
        "Pizza is my favorite food for dinner.",
        "Artificial intelligence is very interesting.",
        "My dog is running in the park.",
        "The book is on the table.",
        "I enjoy reading novels."
    ]
    
    print(f"Text A ({len(text_a)} sentences):")
    for i, sent in enumerate(text_a):
        print(f"  {i+1}. {sent}")
    
    print(f"\nText B ({len(text_b)} sentences):")
    for i, sent in enumerate(text_b):
        print(f"  {i+1}. {sent}")
    
    # Test different modes and pct_k values
    test_configs = [
        (0.5, "bottom"),
        (0.75, "bottom"),
        (1.0, "bottom"),
        (0.5, "top"),
        (0.5, "random")
    ]
    
    for pct_k, mode in test_configs:
        print(f"\n--- Testing pct_k={pct_k}, mode={mode} ---")
        try:
            matched_a, matched_b = sentence_matching(text_a, text_b, pct_k, mode)
            print(f"Matched {len(matched_a)} sentence pairs:")
            for i, (sent_a, sent_b) in enumerate(zip(matched_a, matched_b)):
                print(f"  {i+1}. A: {sent_a}")
                print(f"     B: {sent_b}")
        except Exception as e:
            print(f"Error: {e}")


def test_luq_sentence_matching_uq():
    """Test the LUQSentenceMatchingUQ class."""
    print("\n" + "=" * 60)
    print("Testing LUQSentenceMatchingUQ class")
    print("=" * 60)
    
    # Sample responses for testing
    responses = [
        "The weather is sunny today. I like to eat pizza for dinner. Machine learning is fascinating.",
        "It's a beautiful sunny day outside. Pizza is my favorite food. AI is very interesting.",
        "Today is cloudy and rainy. I prefer pasta over pizza. Deep learning is complex but rewarding.",
        "The sun is shining brightly. Italian cuisine is delicious. Neural networks are powerful tools."
    ]
    
    print("Sample responses:")
    for i, response in enumerate(responses):
        print(f"  {i+1}. {response}")
    
    # Test different configurations
    test_configs = [
        {"pct_k": 0.5, "matching_mode": "bottom"},
        {"pct_k": 0.8, "matching_mode": "bottom"},
        {"pct_k": 1.0, "matching_mode": "bottom"},
        {"pct_k": 0.5, "matching_mode": "top"}
    ]
    
    for config in test_configs:
        print(f"\n--- Testing LUQ Sentence Matching with {config} ---")
        try:
            # Initialize the method
            luq_sm = LUQSentenceMatchingUQ(
                pct_k=config["pct_k"],
                matching_mode=config["matching_mode"],
                verbose=True
            )
            
            # Compute uncertainty
            result = luq_sm.compute_uncertainty(responses)
            
            print(f"Uncertainty Score: {result.get('uncertainty_score', 'N/A'):.4f}")
            print(f"Overall Consistency: {result.get('overall_consistency', 'N/A'):.4f}")
            print(f"Method: {result.get('method', 'N/A')}")
            
            # Print some details about matching
            if 'matching_details' in result:
                total_pairs = sum(
                    sum(info['num_matched_pairs'] for info in detail['matching_info'])
                    for detail in result['matching_details']
                )
                print(f"Total matched sentence pairs: {total_pairs}")
            
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()


def compare_original_vs_sentence_matching():
    """Compare original LUQ with sentence matching variant."""
    print("\n" + "=" * 60)
    print("Comparing Original LUQ vs Sentence Matching LUQ")
    print("=" * 60)
    
    # Sample responses
    responses = [
        "The weather is sunny today. I like to eat pizza for dinner. Machine learning is fascinating.",
        "It's a beautiful sunny day outside. Pizza is my favorite food. AI is very interesting.",
        "Today is cloudy and rainy. I prefer pasta over pizza. Deep learning is complex but rewarding."
    ]
    
    print("Sample responses:")
    for i, response in enumerate(responses):
        print(f"  {i+1}. {response}")
    
    try:
        # Original LUQ
        print("\n--- Original LUQ ---")
        original_luq = LUQUQ(verbose=False)
        original_result = original_luq.compute_uncertainty(responses)
        print(f"Uncertainty Score: {original_result.get('uncertainty_score', 'N/A'):.4f}")
        print(f"Overall Consistency: {original_result.get('overall_consistency', 'N/A'):.4f}")
        
        # Sentence Matching LUQ
        print("\n--- Sentence Matching LUQ ---")
        sm_luq = LUQSentenceMatchingUQ(pct_k=0.8, matching_mode="bottom", verbose=False)
        sm_result = sm_luq.compute_uncertainty(responses)
        print(f"Uncertainty Score: {sm_result.get('uncertainty_score', 'N/A'):.4f}")
        print(f"Overall Consistency: {sm_result.get('overall_consistency', 'N/A'):.4f}")
        
        # Compare
        print("\n--- Comparison ---")
        if 'uncertainty_score' in original_result and 'uncertainty_score' in sm_result:
            diff = sm_result['uncertainty_score'] - original_result['uncertainty_score']
            print(f"Uncertainty difference (SM - Original): {diff:.4f}")
        
    except Exception as e:
        print(f"Error in comparison: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all tests."""
    print("Starting LUQ Sentence Matching Tests")
    print("This may take a few minutes due to model loading...")
    
    try:
        # Test sentence matching function
        test_sentence_matching()
        
        # Test LUQ sentence matching class
        test_luq_sentence_matching_uq()
        
        # Compare methods
        compare_original_vs_sentence_matching()
        
        print("\n" + "=" * 60)
        print("All tests completed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
